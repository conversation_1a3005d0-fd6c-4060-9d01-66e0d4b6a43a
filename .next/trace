[{"name": "hot-reloader", "duration": 234, "timestamp": 363066622837, "id": 3, "tags": {"version": "15.3.5"}, "startTime": 1752002112091, "traceId": "96317164d25a84a0"}, {"name": "setup-dev-bundler", "duration": 1254007, "timestamp": 363066192492, "id": 2, "parentId": 1, "tags": {}, "startTime": 1752002111660, "traceId": "96317164d25a84a0"}, {"name": "run-instrumentation-hook", "duration": 42, "timestamp": 363067578703, "id": 4, "parentId": 1, "tags": {}, "startTime": 1752002113047, "traceId": "96317164d25a84a0"}, {"name": "start-dev-server", "duration": 2792181, "timestamp": 363064844463, "id": 1, "tags": {"cpus": "4", "platform": "darwin", "memory.freeMem": "233803776", "memory.totalMem": "8589934592", "memory.heapSizeLimit": "4345298944", "memory.rss": "198180864", "memory.heapTotal": "97161216", "memory.heapUsed": "75496312"}, "startTime": 1752002110313, "traceId": "96317164d25a84a0"}, {"name": "compile-path", "duration": 16863572, "timestamp": 363084087118, "id": 7, "tags": {"trigger": "/"}, "startTime": 1752002129556, "traceId": "96317164d25a84a0"}, {"name": "ensure-page", "duration": 16871250, "timestamp": 363084082195, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1752002129552, "traceId": "96317164d25a84a0"}]