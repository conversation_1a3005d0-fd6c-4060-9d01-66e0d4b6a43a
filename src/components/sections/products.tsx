"use client"

import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { 
  Smartphone, 
  QrCode, 
  Shield, 
  Users, 
  Wallet, 
  Globe, 
  ArrowRight,
  Zap,
  Database,
  BarChart3
} from "lucide-react"

export function ProductsSection() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  }

  return (
    <section id="products" className="py-24 bg-gradient-to-br from-gray-50 to-purple-50/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
          className="text-center mb-16"
        >
          <motion.h2 
            variants={itemVariants}
            className="text-4xl md:text-5xl font-bold text-gray-900 mb-6"
          >
            Two Powerful Products,
            <br />
            <span className="text-gradient">One Identity Platform</span>
          </motion.h2>
          <motion.p 
            variants={itemVariants}
            className="text-xl text-gray-600 max-w-3xl mx-auto"
          >
            CREFY's modular approach delivers specialized solutions for physical product tokenization 
            and seamless Web3 onboarding, built on a unified identity infrastructure.
          </motion.p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Crefy Phygital */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={itemVariants}
          >
            <Card className="h-full border-2 border-purple-100 hover:border-purple-200 transition-all duration-300 hover:shadow-xl">
              <CardHeader className="text-center pb-8">
                <div className="w-16 h-16 bg-gradient-purple rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <QrCode className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-3xl font-bold text-gray-900">
                  Crefy Phygital
                </CardTitle>
                <CardDescription className="text-lg text-gray-600">
                  Transform physical products into verifiable digital assets with seamless tokenization and lifecycle management.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
                    <Smartphone className="w-5 h-5 text-purple-600" />
                    <span className="text-sm font-medium">Mobile Scanner</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
                    <Shield className="w-5 h-5 text-purple-600" />
                    <span className="text-sm font-medium">Anti-Counterfeit</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
                    <Database className="w-5 h-5 text-purple-600" />
                    <span className="text-sm font-medium">Asset Tracking</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
                    <BarChart3 className="w-5 h-5 text-purple-600" />
                    <span className="text-sm font-medium">Analytics</span>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-900">Key Features:</h4>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-start space-x-2">
                      <div className="w-1.5 h-1.5 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                      <span>RESTful APIs for token creation, minting, and redemption</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <div className="w-1.5 h-1.5 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                      <span>NFC/QR scanner apps for iOS and Android</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <div className="w-1.5 h-1.5 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                      <span>Company dashboard for asset management</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <div className="w-1.5 h-1.5 bg-purple-600 rounded-full mt-2 flex-shrink-0"></div>
                      <span>User portal for claiming and viewing assets</span>
                    </li>
                  </ul>
                </div>

                <Button className="w-full bg-gradient-purple hover:opacity-90 text-white">
                  Explore Phygital
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Button>
              </CardContent>
            </Card>
          </motion.div>

          {/* Crefy Connect */}
          <motion.div
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={itemVariants}
          >
            <Card className="h-full border-2 border-indigo-100 hover:border-indigo-200 transition-all duration-300 hover:shadow-xl">
              <CardHeader className="text-center pb-8">
                <div className="w-16 h-16 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Wallet className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-3xl font-bold text-gray-900">
                  Crefy Connect
                </CardTitle>
                <CardDescription className="text-lg text-gray-600">
                  Seamless social login with embedded smart wallets that abstract away Web3 complexity for users.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3 p-3 bg-indigo-50 rounded-lg">
                    <Users className="w-5 h-5 text-indigo-600" />
                    <span className="text-sm font-medium">Social Auth</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-indigo-50 rounded-lg">
                    <Zap className="w-5 h-5 text-indigo-600" />
                    <span className="text-sm font-medium">Smart Wallets</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-indigo-50 rounded-lg">
                    <Globe className="w-5 h-5 text-indigo-600" />
                    <span className="text-sm font-medium">ENS Integration</span>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-indigo-50 rounded-lg">
                    <BarChart3 className="w-5 h-5 text-indigo-600" />
                    <span className="text-sm font-medium">Dev Analytics</span>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-900">Supported Login Methods:</h4>
                  <div className="grid grid-cols-3 gap-2 text-sm">
                    <div className="bg-gray-50 p-2 rounded text-center">Twitter</div>
                    <div className="bg-gray-50 p-2 rounded text-center">Google</div>
                    <div className="bg-gray-50 p-2 rounded text-center">Email</div>
                    <div className="bg-gray-50 p-2 rounded text-center">Phone</div>
                    <div className="bg-gray-50 p-2 rounded text-center">Discord</div>
                    <div className="bg-gray-50 p-2 rounded text-center">EOA</div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-900">Key Features:</h4>
                  <ul className="space-y-2 text-gray-600">
                    <li className="flex items-start space-x-2">
                      <div className="w-1.5 h-1.5 bg-indigo-600 rounded-full mt-2 flex-shrink-0"></div>
                      <span>Account Abstraction with smart contract wallets</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <div className="w-1.5 h-1.5 bg-indigo-600 rounded-full mt-2 flex-shrink-0"></div>
                      <span>Auto-assigned ENS names and subdomains</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <div className="w-1.5 h-1.5 bg-indigo-600 rounded-full mt-2 flex-shrink-0"></div>
                      <span>Integrated fiat on/off ramps</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <div className="w-1.5 h-1.5 bg-indigo-600 rounded-full mt-2 flex-shrink-0"></div>
                      <span>Developer analytics and monitoring</span>
                    </li>
                  </ul>
                </div>

                <Button className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:opacity-90 text-white">
                  Explore Connect
                  <ArrowRight className="ml-2 w-4 h-4" />
                </Button>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
